import type React from "react";

export type ButtonVariant = "default" | "ghost" | "outline";
export type ButtonSize = "default" | "icon" | "sm";

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
  variant?: ButtonVariant;
  size?: ButtonSize;
  className?: string;
}

export const Button = ({
  children,
  onClick,
  variant = "default",
  size = "default",
  className = "",
  ...props
}: ButtonProps) => {
  const baseClasses =
    "inline-flex items-center justify-center font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background";

  const variants: Record<ButtonVariant, string> = {
    default: "bg-blue-600 text-white hover:bg-blue-700",
    ghost: "hover:bg-gray-100 hover:text-gray-900",
    outline: "border border-gray-300 bg-transparent hover:bg-gray-50",
  };

  const sizes: Record<ButtonSize, string> = {
    default: "h-10 py-2 px-4",
    icon: "h-10 w-10",
    sm: "h-8 px-3 text-sm",
  };

  // Default rounded class that can be overridden
  const defaultRounded = className.includes("rounded-") ? "" : "rounded-md";

  return (
    <button
      className={`${baseClasses} ${defaultRounded} ${variants[variant]} ${sizes[size]} ${className}`}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};
