"use client";

import { Star } from "lucide-react";

interface SimilarApp {
  id: number;
  name: string;
  rating: number;
  icon: string;
  backgroundColor: string;
}

export default function SimilarAppsSection() {
  const similarApps: SimilarApp[] = [
    {
      id: 2,
      name: "Teen Patti Gold, Rummy & Poker",
      rating: 4.7,
      icon: "/placeholder.svg?height=120&width=120&text=TEENPATTI",
      backgroundColor: "bg-gradient-to-br from-red-400 to-orange-500",
    },
    {
      id: 3,
      name: "DoubleDown Casino Vegas Pokies",
      rating: 4.0,
      icon: "/placeholder.svg?height=120&width=120&text=7+UP+DOWN",
      backgroundColor: "bg-gradient-to-br from-purple-500 to-purple-700",
    },
    {
      id: 4,
      name: "DoubleDown Casino Vegas Pokies",
      rating: 4.0,
      icon: "/placeholder.svg?height=120&width=120&text=SLOT+GAMES",
      backgroundColor: "bg-gradient-to-br from-purple-600 to-pink-600",
    },
    {
      id: 5,
      name: "<PERSON>um<PERSON> Gold (With Fast Rummy)",
      rating: 4.5,
      icon: "/placeholder.svg?height=120&width=120&text=RUMMY",
      backgroundColor: "bg-gradient-to-br from-green-500 to-green-700",
    },
    {
      id: 6,
      name: "Poker Offline",
      rating: 4.3,
      icon: "/placeholder.svg?height=120&width=120&text=POKER",
      backgroundColor: "bg-gradient-to-br from-blue-600 to-blue-800",
    },
    {
      id: 1,
      name: "Aviator",
      rating: 4.9,
      icon: "/placeholder.svg?height=120&width=120&text=AVIATOR",
      backgroundColor: "bg-gradient-to-br from-red-600 to-red-800",
    },
  ];

  return (
    <div className="mobile-padding-sm border-t border-gray-100">
      {/* Similar apps section */}
      <div>
        <h2 className="mobile-heading-sm text-gray-900 mb-4">Similar apps</h2>

        {/* Apps grid - Responsive */}
        <div className="overflow-x-auto scrollbar-hide">
          <div
            className="flex mobile-gap-sm pb-4"
            style={{ width: "max-content" }}
          >
            {similarApps.map((app, index) => (
              <div key={app.id} className="flex-shrink-0 w-20 xs:w-24 sm:w-28">
                {/* App icon - Responsive sizing */}
                <div className="rounded-2xl overflow-hidden relative w-20 h-24 xs:w-24 xs:h-32 sm:w-28 sm:h-36">
                  {index === 0 && <TeenpattiIcon />}
                  {index === 1 && <SevenUpDownIcon />}
                  {index === 2 && <SlotGamesIcon />}
                  {index === 3 && <RummyIcon />}
                  {index === 4 && <PokerIcon />}
                  {index === 5 && <AviatorIcon />}
                </div>

                {/* App name */}
                <h3 className="text-xs font-normal text-gray-900 mb-1 leading-tight">
                  {app.name}
                </h3>

                {/* Rating */}
                <div className="flex items-center mobile-gap-xs">
                  <span className="mobile-text-xs xs:mobile-text-sm font-medium text-gray-900">
                    {app.rating}
                  </span>
                  <Star className="w-3 h-3 xs:w-3 xs:h-3 fill-gray-400 text-gray-400" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Individual app icon components
const AviatorIcon = () => (
  <div className="w-full aspect-square relative overflow-hidden bg-gradient-to-br from-gray-700 to-gray-900 rounded-2xl">
    <img
      src="/similarapps/avatior.png"
      alt="Aviator"
      className="w-full h-full object-contain"
    />
  </div>
);

const TeenpattiIcon = () => (
  <div className="w-full aspect-square relative overflow-hidden">
    <img
      src="/similarapps/tripatii.png"
      alt="Teenpatti"
      className="w-full h-full object-contain"
    />
  </div>
);

const SevenUpDownIcon = () => (
  <div className="w-full aspect-square relative overflow-hidden">
    <img
      src="/similarapps/7updown-removebg-preview.png"
      alt="7 Up Down"
      className="w-full h-full object-contain"
    />
  </div>
);

const SlotGamesIcon = () => (
  <div className="w-full aspect-square relative overflow-hidden">
    <img
      src="/similarapps/slotgame-removebg-preview.png"
      alt="Slot Games"
      className="w-full h-full object-contain"
    />
  </div>
);

const RummyIcon = () => (
  <div className="w-full aspect-square relative overflow-hidden">
    <img
      src="/similarapps/rummy-removebg-preview.png"
      alt="Rummy"
      className="w-full h-full object-contain"
    />
  </div>
);

const PokerIcon = () => (
  <div
    className="w-full aspect-square relative overflow-hidden rounded-2xl"
    style={{
      background:
        "radial-gradient(circle at center, #32CD32 0%, #228B22 70%, #006400 100%)",
    }}
  >
    <img
      src="/similarapps/poker-removebg-preview.png"
      alt="Poker"
      className="w-full h-full object-contain"
    />
  </div>
);
