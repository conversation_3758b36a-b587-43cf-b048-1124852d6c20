"use client";

export default function ScreenshotCarousel() {
  const screenshots = [
    {
      id: 1,
      title: "Bet Smart.",
      subtitle: "Win Big Tonight!",
      image: "/pannal1.png",
      description: "Smart betting interface with live odds",
    },
    {
      id: 2,
      title: "Join RamjiBookies",
      subtitle: "Start Winning!",
      image: "/pannal2.png",
      description: "Easy registration and login process",
    },
    {
      id: 3,
      title: "24/7 Support",
      subtitle: "We're Here for You!",
      image: "/pannal3.png",
      description: "Round the clock customer support",
    },
    {
      id: 4,
      title: "Cr",
      subtitle: "",
      image: "/pannal4.png",
      description: "Cricket statistics and analysis",
    },
  ];

  return (
    <div className="mobile-padding-sm border-t border-gray-100">
      {/* Screenshots carousel - Responsive */}
      <div className="overflow-x-auto scrollbar-hide">
        <div
          className="flex mobile-gap-sm pb-4"
          style={{ width: "max-content" }}
        >
          {screenshots.map((screenshot) => (
            <div key={screenshot.id} className="flex-shrink-0">
              {/* Screenshot - Responsive sizing */}
              <div className="w-24 h-48 xs:w-28 xs:h-56 sm:w-32 sm:h-64 overflow-hidden rounded-lg shadow-lg">
                <img
                  src={screenshot.image}
                  alt={screenshot.description}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
