import { Icon } from "@iconify/react";
import { Button } from "../ui/Button";
import { GooglePlayIcon } from "../icons/GooglePlayIcon";

export const GooglePlayHeader = () => {
  return (
    <div className="flex items-center justify-between mobile-padding-sm border-b border-gray-100 mobile-safe-top">
      <div className="flex items-center mobile-gap-sm">
        <GooglePlayIcon />
        <span className="mobile-heading-md text-[#5F6368]">Google Play</span>
      </div>
      <div className="flex items-center mobile-gap-xs">
        <Button variant="ghost" size="icon" className="touch-manipulation">
          <Icon
            icon="iconamoon:search"
            className="mobile-icon-sm text-gray-600"
          />
        </Button>
        <Button variant="ghost" size="icon" className="touch-manipulation">
          <Icon
            icon="material-symbols:info-outline"
            className="mobile-icon-sm text-gray-600"
          />
        </Button>
        <Button variant="ghost" size="icon" className="touch-manipulation">
          <Icon
            icon="material-symbols:more-horiz"
            className="mobile-icon-sm text-gray-600"
          />
        </Button>
      </div>
    </div>
  );
};
