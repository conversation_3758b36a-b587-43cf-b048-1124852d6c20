import React from "react";
import MobileApp from "./MobileApp";
import Sidebar from "./Sidebar";

interface ResponsiveLayoutProps {
  children?: React.ReactNode;
}

export default function ResponsiveLayout({ children }: ResponsiveLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Layout - Full width on mobile and small tablets */}
      <div className="block lg:hidden">
        <MobileApp />
      </div>

      {/* Desktop Layout - Google Play Store inspired layout */}
      <div className="hidden lg:block min-h-screen">
        {/* Container with 5% margins on both sides */}
        <div className="desktop-container">
          {/* Main content area with 60/40 split */}
          <div className="desktop-content-grid">
            {/* Left Section - 60% width - Mobile content */}
            <div className="desktop-main-content">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden h-full">
                <MobileApp />
              </div>
            </div>

            {/* Right Section - 40% width - Sidebar with placeholder content */}
            <div className="desktop-sidebar">
              <Sidebar variant="desktop" />
            </div>
          </div>
        </div>
      </div>

      {/* Tablet Layout - Intermediate responsive behavior */}
      <div className="hidden md:block lg:hidden">
        <div className="tablet-container">
          <div className="tablet-content-grid">
            {/* Tablet uses a slightly different layout - more mobile-like but with some desktop elements */}
            <div className="tablet-main-content">
              <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <MobileApp />
              </div>
            </div>

            {/* Smaller sidebar on tablet */}
            <div className="tablet-sidebar">
              <Sidebar variant="tablet" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
