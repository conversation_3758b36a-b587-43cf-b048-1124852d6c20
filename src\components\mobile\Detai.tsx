"use client";

import { Icon } from "@iconify/react";

export default function Detai() {
  return (
    <div className="bg-gray-100 border-gray-500 shadow-sm rounded-md mobile-padding-sm mobile-margin-sm">
      <div className="space-y-3 xs:space-y-4">
        {/* Data sharing */}
        <div className="flex items-start mobile-gap-sm">
          <Icon
            icon="material-symbols:share-outline"
            className="mobile-icon-sm text-[#5F6368] mt-0.5 flex-shrink-0"
          />
          <div className="flex-1">
            <p className="text-[#5F6368] font-medium mobile-text-sm leading-relaxed">
              This app may share these data types third parties
            </p>
            <p className="text-[#5F6368] mobile-text-sm">
              Location, Personal info and 6 others
            </p>
          </div>
        </div>

        {/* Data collection */}
        <div className="flex items-start mobile-gap-sm">
          <Icon
            icon="material-symbols:cloud-download-outline"
            className="mobile-icon-sm text-[#5F6368] mt-0.5 flex-shrink-0"
          />
          <div className="flex-1">
            <p className="text-[#5F6368] font-medium mobile-text-sm leading-relaxed">
              This app may collect these data types Location, Personal info and
              6 others
            </p>
          </div>
        </div>

        {/* Data encryption */}
        <div className="flex items-start mobile-gap-sm">
          <img
            src="/octicon_no-entry-24.svg"
            alt="Lock"
            className="mobile-icon-sm -mt-0.5 flex-shrink-0"
          />
          <div className="flex-1">
            <p className="text-[#5F6368] font-medium mobile-text-sm">
              Data is encrypted in transit
            </p>
          </div>
        </div>

        {/* Data deletion */}
        <div className="flex items-start mobile-gap-sm">
          <Icon
            icon="material-symbols:delete-outline"
            className="mobile-icon-sm text-[#5F6368] -mt-0.5 flex-shrink-0"
          />
          <div className="flex-1">
            <p className="text-[#5F6368] font-medium mobile-text-sm">
              You can request that data be deleted
            </p>
          </div>
        </div>

        {/* See details link */}
        <div className="pt-2">
          <button className="text-blue-600 font-medium mobile-text-sm hover:text-blue-700 transition-colors touch-manipulation">
            See details
          </button>
        </div>
      </div>
    </div>
  );
}
