// import type React from "react";

// export interface BadgeProps {
//   children: React.ReactNode;
//   className?: string;
// }

// export const Badge = ({ children, className = "", ...props }: BadgeProps) => {
//   return (
//     <div
//       className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-gray-100 text-gray-700 ${className}`}
//       {...props}
//     >
//       {children}
//     </div>
//   );
// };
