@layer utilities {
/_ Mobile-First Responsive Container System _/
.mobile-container-xs {
@apply w-full max-w-sm mx-auto px-3;
}

.mobile-container-sm {
@apply w-full max-w-md mx-auto px-4;
}

.mobile-container-md {
@apply w-full max-w-lg mx-auto px-4;
}

.mobile-container-lg {
@apply w-full max-w-xl mx-auto px-5;
}

.mobile-container-xl {
@apply w-full max-w-2xl mx-auto px-6;
}

/_ Mobile-Specific Typography System _/
.mobile-text-xs {
@apply text-xs leading-tight;
}

.mobile-text-sm {
@apply text-sm leading-snug;
}

.mobile-text-base {
@apply text-sm xs:text-base leading-normal;
}

.mobile-text-lg {
@apply text-base xs:text-lg leading-relaxed;
}

.mobile-heading-sm {
@apply text-lg xs:text-xl font-medium leading-tight;
}

.mobile-heading-md {
@apply text-xl xs:text-2xl font-semibold leading-tight;
}

.mobile-heading-lg {
@apply text-2xl xs:text-3xl sm:text-4xl font-bold leading-tight;
}

/_ Mobile-Specific Spacing System _/
.mobile-spacing-xs {
@apply p-2 xs:p-3;
}

.mobile-spacing-sm {
@apply p-3 xs:p-4;
}

.mobile-spacing-md {
@apply p-4 xs:p-5;
}

.mobile-spacing-lg {
@apply p-5 xs:p-6;
}

/_ Mobile Gap System _/
.mobile-gap-xs {
@apply gap-2 xs:gap-3;
}

.mobile-gap-sm {
@apply gap-3 xs:gap-4;
}

.mobile-gap-md {
@apply gap-4 xs:gap-5;
}

.mobile-gap-lg {
@apply gap-5 xs:gap-6;
}

/_ Mobile Margin System _/
.mobile-margin-xs {
@apply m-2 xs:m-3;
}

.mobile-margin-sm {
@apply m-3 xs:m-4;
}

.mobile-margin-md {
@apply m-4 xs:m-5;
}

/_ Mobile Padding System _/
.mobile-padding-xs {
@apply px-3 py-2 xs:px-4 xs:py-3;
}

.mobile-padding-sm {
@apply px-4 py-3 xs:px-5 xs:py-4;
}

.mobile-padding-md {
@apply px-5 py-4 xs:px-6 xs:py-5;
}

/_ Mobile Button Sizes _/
.mobile-btn-sm {
@apply h-8 px-3 text-sm xs:h-9 xs:px-4;
}

.mobile-btn-md {
@apply h-10 px-4 text-sm xs:h-11 xs:px-5 xs:text-base;
}

.mobile-btn-lg {
@apply h-12 px-6 text-base xs:h-14 xs:px-8 xs:text-lg;
}

/_ Mobile Icon Sizes _/
.mobile-icon-xs {
@apply w-4 h-4 xs:w-5 xs:h-5;
}

.mobile-icon-sm {
@apply w-5 h-5 xs:w-6 xs:h-6;
}

.mobile-icon-md {
@apply w-6 h-6 xs:w-7 xs:h-7;
}

.mobile-icon-lg {
@apply w-8 h-8 xs:w-10 xs:h-10;
}

/_ Mobile Layout Utilities _/
.mobile-flex-col {
@apply flex flex-col;
}

.mobile-flex-row {
@apply flex flex-row;
}

.mobile-grid-1 {
@apply grid grid-cols-1;
}

.mobile-grid-2 {
@apply grid grid-cols-2 xs:grid-cols-2;
}

.mobile-grid-3 {
@apply grid grid-cols-2 xs:grid-cols-3;
}

.mobile-grid-4 {
@apply grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-4;
}

/_ Mobile Safe Area _/
.mobile-safe-top {
@apply pt-safe-top;
}

.mobile-safe-bottom {
@apply pb-safe-bottom;
}

.mobile-safe-area {
@apply pt-safe-top pb-safe-bottom;
}
}
