// Responsive mobile device breakpoints and dimensions
export const MO<PERSON>LE_BREAKPOINTS = {
  XS: 320, // Very small phones (iPhone SE 1st gen, small Android)
  SM: 375, // Small phones (iPhone SE 2nd/3rd gen, iPhone 12 mini)
  MD: 414, // Standard phones (iPhone 12/13/14 Pro, most Android)
  LG: 428, // Large phones (iPhone 12/13/14 Pro Max)
  XL: 480, // Extra large phones (large Android devices)
} as const;

// Responsive device dimensions - now supports multiple screen sizes
export const DEVICE_DIMENSIONS = {
  // Responsive frame dimensions (will be handled by CSS)
  FRAME_WIDTH_MIN: 320,
  FRAME_WIDTH_MAX: 480,
  FRAME_HEIGHT_MIN: 568, // iPhone SE height
  FRAME_HEIGHT_MAX: 932, // iPhone 14 Pro Max height

  // Border radius scales with screen size
  BORDER_RADIUS: {
    XS: 40,
    SM: 50,
    MD: 60,
    LG: 60,
    XL: 60,
  },

  // Dynamic island dimensions (responsive)
  DYNAMIC_ISLAND: {
    WIDTH: { XS: 120, SM: 130, MD: 144, LG: 144, XL: 144 },
    HEIGHT: { XS: 28, SM: 30, MD: 32, LG: 32, XL: 32 },
  },

  // Home indicator dimensions (responsive)
  HOME_INDICATOR: {
    WIDTH: { XS: 120, SM: 130, MD: 144, LG: 144, XL: 144 },
    HEIGHT: 4, // Consistent across all sizes
  },
} as const;

export const APP_DATA = {
  name: "Ramji Play",
  developer: "Product Madness",
  rating: 4.9,
  reviews: "15k reviews",
  downloads: "750k",
  size: "200 MB",
  ageRating: "18+",
  description:
    "Ramji Bookies is the go-to app for sports fans to test their game knowledge and make winning predictions—cricket, football, or kabaddi—all the thrill, none of the risk.",
} as const;

export const STATUS_BAR_TIME = "9:41" as const;
