"use client";

import { Icon } from "@iconify/react";
import { MoreVertical } from "lucide-react";

interface ReviewItemProps {
  userName: string;
  rating: number;
  date: string;
  reviewText: string;
  helpfulCount: number;
  onHelpfulClick: (helpful: boolean) => void;
}

export default function ReviewItem({
  userName,
  rating,
  date,
  reviewText,
  helpfulCount,
  onHelpfulClick,
}: ReviewItemProps) {
  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Icon
          key={i}
          icon="material-symbols:star"
          className={`h-4 w-4 ${
            i <= rating ? "text-blue-500" : "text-gray-300"
          }`}
        />
      );
    }
    return stars;
  };

  return (
    <div className="mobile-padding-sm border-b border-gray-100 last:border-b-0">
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center mobile-gap-sm">
          {/* User Avatar - Responsive */}
          <div className="w-8 h-8 xs:w-10 xs:h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden">
            <img
              src="/avatar.png"
              alt={userName}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
                target.nextElementSibling?.classList.remove("hidden");
              }}
            />
            <div className="hidden w-full h-full bg-blue-500 items-center justify-center text-white mobile-text-xs font-medium">
              {userName.charAt(0).toUpperCase()}
            </div>
          </div>
          {/* User Info */}
          <div>
            <h3 className="font-medium text-gray-900 mobile-text-sm">
              {userName}
            </h3>
          </div>
        </div>
        {/* More Options */}
        <button className="p-1 hover:bg-gray-100 rounded-full touch-manipulation">
          <MoreVertical className="mobile-icon-sm text-gray-800" />
        </button>
      </div>

      {/* Rating and Date - separate row */}
      <div className="flex items-center mobile-gap-xs mb-3 xs:mb-4 ml-10 xs:ml-13">
        <div className="flex items-center">{renderStars(rating)}</div>
        <span className="text-gray-500 mobile-text-xs">{date}</span>
      </div>

      {/* Review Text */}
      <p className="text-gray-500 mobile-text-sm leading-relaxed mb-3 xs:mb-4 ml-10 xs:ml-13">
        {reviewText}
      </p>
      <p className="text-gray-500 mobile-text-sm leading-relaxed mb-3 xs:mb-4 ml-10 xs:ml-13">
        {helpfulCount} people found this helpful
      </p>

      {/* Actions - Responsive */}
      <div className="flex flex-col xs:flex-row xs:items-center xs:justify-between ml-10 xs:ml-13 mobile-gap-xs">
        <span className="text-gray-600 mobile-text-sm">
          Was this review helpful?
        </span>
        <div className="flex items-center mobile-gap-xs">
          <button
            onClick={() => onHelpfulClick(true)}
            className="border border-gray-300 mobile-btn-sm rounded-md text-gray-700 hover:bg-gray-50 touch-manipulation"
          >
            Yes
          </button>
          <button
            onClick={() => onHelpfulClick(false)}
            className="border border-gray-300 mobile-btn-sm rounded-md text-gray-700 hover:bg-gray-50 touch-manipulation"
          >
            No
          </button>
        </div>
      </div>
    </div>
  );
}
