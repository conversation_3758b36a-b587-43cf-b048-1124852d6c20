"use client";

// Components
import { Button } from "./ui";
import {
  GooglePlayHeader,
  AppHeaderSection,
  ScreenshotCarousel,
  AboutSection,
  DataSafetySection,
  Detai,
  RatingsSection,
  ReviewsSection,
  SimilarAppsSection,
  NavigationBar,
} from "./mobile";

interface MobileAppProps {
  onBack?: () => void;
}

export default function MobileApp({ onBack }: MobileAppProps = {}) {
  return (
    <div className="min-h-screen mobile-full-height bg-gray-100 w-full lg:bg-transparent lg:min-h-0">
      {/* Back to Main App Button - Only show on larger screens */}
      {onBack && (
        <Button
          onClick={onBack}
          variant="outline"
          className="fixed top-4 left-4 z-50 bg-white shadow-lg hidden md:flex"
        >
          ← Back to Main
        </Button>
      )}

      {/* Mobile App Container - Responsive */}
      <div className="w-full h-full mobile-full-height flex flex-col lg:h-auto">
        {/* Mobile App Frame - Responsive */}
        <div className="w-full max-w-mobile-xl mx-auto bg-white shadow-lg mobile-full-height flex flex-col relative overflow-hidden lg:max-w-none lg:shadow-none lg:mobile-full-height-none">
          {/* Status Bar - Hidden on mobile for now */}
          {/* <StatusBar /> */}

          {/* App Content - Scrollable */}
          <div className="flex-1 bg-white overflow-y-auto scrollbar-hide mobile-safe-area lg:overflow-visible lg:flex-none">
            <GooglePlayHeader />
            <AppHeaderSection />
            <ScreenshotCarousel />
            <AboutSection />
            <DataSafetySection />
            <Detai />
            <RatingsSection />
            <ReviewsSection />
            <SimilarAppsSection />
          </div>

          {/* Bottom Navigation - Fixed at bottom */}
          <NavigationBar />
        </div>
      </div>
    </div>
  );
}
