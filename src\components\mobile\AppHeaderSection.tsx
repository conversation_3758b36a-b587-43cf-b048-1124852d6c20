import { Download, <PERSON> } from "lucide-react";
import { But<PERSON> } from "../ui/Button";
import { AppIcon } from "../icons/AppIcon";
import { APP_DATA } from "../../constants/mobileApp";

export const AppHeaderSection = () => {
  return (
    <div className="mobile-padding-sm">
      <div className="flex items-start mobile-gap-sm">
        <AppIcon />
        <div className="flex-1">
          <h1 className="mobile-heading-md text-gray-900">{APP_DATA.name}</h1>
          <p className="text-[#3B974A] mobile-text-base font-medium">
            {APP_DATA.developer}
          </p>
          <p className="text-[#9AA0A6] mobile-text-sm">In-app purchase</p>
        </div>
      </div>

      {/* Stats Row - Responsive */}
      <div className="flex items-center justify-between mt-4 xs:mt-6 mx-2 xs:mx-4 sm:mx-8 mb-4 xs:mb-6">
        <div className="text-center flex-1">
          <div className="flex items-center justify-center mb-1">
            <span className="mobile-text-base xs:text-lg font-medium mr-1">
              {APP_DATA.rating}
            </span>
            <Star className="mobile-icon-xs fill-[#6666F1] text-[#6666F1]" />
          </div>
          <p className="mobile-text-xs text-gray-500">{APP_DATA.reviews}</p>
        </div>

        {/* Vertical Divider */}
        <div className="h-8 xs:h-12 w-px bg-gray-300 mx-1 xs:mx-2"></div>

        <div className="text-center flex-1">
          <p className="mobile-text-sm xs:text-sm font-medium">
            {APP_DATA.downloads}
          </p>
          <p className="mobile-text-xs text-gray-500">Downloads</p>
        </div>

        {/* Vertical Divider */}
        <div className="h-8 xs:h-12 w-px bg-gray-300 mx-1 xs:mx-2"></div>

        <div className="text-center flex-1">
          <Download className="mobile-icon-sm text-gray-900 mx-auto mb-1" />
          <p className="mobile-text-xs text-gray-500">Size</p>
        </div>

        {/* Vertical Divider */}
        <div className="h-8 xs:h-12 w-px bg-gray-300 mx-1 xs:mx-2"></div>

        <div className="text-center flex-1">
          <p className="mobile-text-sm xs:text-sm font-medium mb-1">
            {APP_DATA.ageRating}
          </p>
          <div className="flex items-center justify-center">
            <span className="mobile-text-xs text-gray-500 mr-1">
              {APP_DATA.ageRating}
            </span>
            <img
              src="/si_info-line.svg"
              alt="Info"
              className="mobile-icon-xs"
            />
          </div>
        </div>
      </div>

      {/* Install Button - Responsive */}
      <Button
        className="w-full mobile-btn-lg bg-[#1A73E8] hover:bg-[#1565C0] text-white font-medium rounded-full"
        onClick={() =>
          window.open("https://ramjibookies.com/app-download", "_blank")
        }
      >
        Install
      </Button>
    </div>
  );
};
