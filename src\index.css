@import url("https://fonts.googleapis.com/css2?family=Product+Sans:wght@300;400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apply Product Sans font globally */
@layer base {
  * {
    font-family: "Product Sans", system-ui, sans-serif;
  }

  body {
    font-family: "Product Sans", system-ui, sans-serif;
  }

  /* Mobile viewport optimizations */
  html {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  /* Ensure proper mobile viewport handling */
  @supports (height: 100dvh) {
    html,
    body {
      min-height: 100dvh;
    }
  }
}

/* Hide scrollbar while maintaining scroll functionality */
@layer utilities {
  .scrollbar-hide {
    /* Hide scrollbar for Chrome, Safari and Opera */
    -webkit-scrollbar: none;
    /* Hide scrollbar for IE, Edge and Firefox */
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Mobile-specific utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .prevent-zoom {
    touch-action: pan-x pan-y;
  }

  .mobile-full-height {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
  }

  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Mobile-First Responsive Container System */
  .mobile-container-xs {
    @apply w-full max-w-sm mx-auto px-3;
  }

  .mobile-container-sm {
    @apply w-full max-w-md mx-auto px-4;
  }

  .mobile-container-md {
    @apply w-full max-w-lg mx-auto px-4;
  }

  .mobile-container-lg {
    @apply w-full max-w-xl mx-auto px-5;
  }

  .mobile-container-xl {
    @apply w-full max-w-2xl mx-auto px-6;
  }

  /* Mobile-Specific Typography System */
  .mobile-text-xs {
    @apply text-xs leading-tight;
  }

  .mobile-text-sm {
    @apply text-sm leading-snug;
  }

  .mobile-text-base {
    @apply text-sm xs:text-base leading-normal;
  }

  .mobile-text-lg {
    @apply text-base xs:text-lg leading-relaxed;
  }

  .mobile-heading-sm {
    @apply text-lg xs:text-xl font-medium leading-tight;
  }

  .mobile-heading-md {
    @apply text-xl xs:text-2xl font-semibold leading-tight;
  }

  .mobile-heading-lg {
    @apply text-2xl xs:text-3xl sm:text-4xl font-bold leading-tight;
  }

  /* Mobile-Specific Spacing System */
  .mobile-spacing-xs {
    @apply p-2 xs:p-3;
  }

  .mobile-spacing-sm {
    @apply p-3 xs:p-4;
  }

  .mobile-spacing-md {
    @apply p-4 xs:p-5;
  }

  .mobile-spacing-lg {
    @apply p-5 xs:p-6;
  }

  /* Mobile Gap System */
  .mobile-gap-xs {
    @apply gap-2 xs:gap-3;
  }

  .mobile-gap-sm {
    @apply gap-3 xs:gap-4;
  }

  .mobile-gap-md {
    @apply gap-4 xs:gap-5;
  }

  .mobile-gap-lg {
    @apply gap-5 xs:gap-6;
  }

  /* Mobile Margin System */
  .mobile-margin-xs {
    @apply m-2 xs:m-3;
  }

  .mobile-margin-sm {
    @apply m-3 xs:m-4;
  }

  .mobile-margin-md {
    @apply m-4 xs:m-5;
  }

  /* Mobile Padding System */
  .mobile-padding-xs {
    @apply px-3 py-2 xs:px-4 xs:py-3;
  }

  .mobile-padding-sm {
    @apply px-4 py-3 xs:px-5 xs:py-4;
  }

  .mobile-padding-md {
    @apply px-5 py-4 xs:px-6 xs:py-5;
  }

  /* Mobile Button Sizes */
  .mobile-btn-sm {
    @apply h-8 px-3 text-sm xs:h-9 xs:px-4;
  }

  .mobile-btn-md {
    @apply h-10 px-4 text-sm xs:h-11 xs:px-5 xs:text-base;
  }

  .mobile-btn-lg {
    @apply h-12 px-6 text-base xs:h-14 xs:px-8 xs:text-lg;
  }

  /* Mobile Icon Sizes */
  .mobile-icon-xs {
    @apply w-4 h-4 xs:w-5 xs:h-5;
  }

  .mobile-icon-sm {
    @apply w-5 h-5 xs:w-6 xs:h-6;
  }

  .mobile-icon-md {
    @apply w-6 h-6 xs:w-7 xs:h-7;
  }

  .mobile-icon-lg {
    @apply w-8 h-8 xs:w-10 xs:h-10;
  }

  /* Desktop Layout System - Google Play Store inspired */
  .desktop-container {
    @apply min-h-screen px-[5%] py-6;
  }

  .desktop-content-grid {
    @apply grid grid-cols-5 gap-6 h-full max-w-7xl mx-auto;
  }

  .desktop-main-content {
    @apply col-span-3 min-h-screen;
  }

  .desktop-sidebar {
    @apply col-span-2 min-h-screen;
  }

  /* Tablet Layout System */
  .tablet-container {
    @apply min-h-screen px-[3%] py-4;
  }

  .tablet-content-grid {
    @apply grid grid-cols-4 gap-4 h-full max-w-5xl mx-auto;
  }

  .tablet-main-content {
    @apply col-span-3 min-h-screen;
  }

  .tablet-sidebar {
    @apply col-span-1 min-h-screen;
  }

  /* Responsive transitions */
  .responsive-transition {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Desktop-specific overrides for mobile components */
  .mobile-full-height-none {
    height: auto !important;
  }

  /* Smooth scrolling for desktop */
  @media (min-width: 1024px) {
    .desktop-smooth-scroll {
      scroll-behavior: smooth;
    }

    /* Custom scrollbar for desktop */
    .desktop-scrollbar::-webkit-scrollbar {
      width: 6px;
    }

    .desktop-scrollbar::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    .desktop-scrollbar::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    .desktop-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  /* Tablet-specific adjustments */
  @media (min-width: 768px) and (max-width: 1023px) {
    .tablet-optimized {
      padding: 1rem;
    }
  }
}

/* Custom animations for reviews section */
@layer utilities {
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes slideOutDown {
    from {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
  }

  .animate-slideInUp {
    animation: slideInUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  .animate-slideOutDown {
    animation: slideOutDown 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19) forwards;
  }
}
