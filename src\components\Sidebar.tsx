import React from 'react';

interface SidebarProps {
  variant?: 'desktop' | 'tablet';
}

export default function Sidebar({ variant = 'desktop' }: SidebarProps) {
  const isDesktop = variant === 'desktop';
  
  return (
    <div className={`bg-white rounded-lg shadow-sm ${isDesktop ? 'p-6' : 'p-4'} h-full`}>
      {/* Header */}
      <div className="mb-6">
        <h2 className={`font-semibold text-gray-900 ${isDesktop ? 'text-lg' : 'text-base'} mb-2`}>
          Related Content
        </h2>
        <div className="w-12 h-1 bg-green-500 rounded-full"></div>
      </div>

      {/* Placeholder Content Sections */}
      <div className="space-y-6">
        {/* Similar Apps Section */}
        <div>
          <h3 className={`font-medium text-gray-700 ${isDesktop ? 'text-base' : 'text-sm'} mb-3`}>
            Similar Apps
          </h3>
          <div className="space-y-3">
            {[1, 2, 3].map((item) => (
              <div key={item} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                <div className={`${isDesktop ? 'w-10 h-10' : 'w-8 h-8'} bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg flex-shrink-0`}></div>
                <div className="flex-1 min-w-0">
                  <div className={`${isDesktop ? 'h-3' : 'h-2'} bg-gray-200 rounded mb-1`}></div>
                  <div className={`${isDesktop ? 'h-2' : 'h-1.5'} bg-gray-100 rounded w-2/3`}></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recommendations Section */}
        <div>
          <h3 className={`font-medium text-gray-700 ${isDesktop ? 'text-base' : 'text-sm'} mb-3`}>
            Recommended for You
          </h3>
          <div className="space-y-3">
            {[1, 2].map((item) => (
              <div key={item} className="p-3 border border-gray-100 rounded-lg">
                <div className={`${isDesktop ? 'h-3' : 'h-2'} bg-gray-200 rounded mb-2`}></div>
                <div className={`${isDesktop ? 'h-2' : 'h-1.5'} bg-gray-100 rounded w-3/4 mb-2`}></div>
                <div className="flex items-center space-x-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <div key={star} className={`${isDesktop ? 'w-3 h-3' : 'w-2 h-2'} bg-yellow-300 rounded-sm`}></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Developer Info Section */}
        {isDesktop && (
          <div>
            <h3 className="font-medium text-gray-700 text-base mb-3">
              Developer Info
            </h3>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-green-500 rounded-full flex-shrink-0"></div>
                <div className="flex-1">
                  <div className="h-3 bg-gray-200 rounded mb-1"></div>
                  <div className="h-2 bg-gray-100 rounded w-1/2"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="h-2 bg-gray-100 rounded"></div>
                <div className="h-2 bg-gray-100 rounded w-4/5"></div>
              </div>
            </div>
          </div>
        )}

        {/* Additional Content Placeholder */}
        <div className="text-center py-8">
          <div className={`${isDesktop ? 'w-16 h-16' : 'w-12 h-12'} bg-gray-100 rounded-full mx-auto flex items-center justify-center mb-4`}>
            <svg className={`${isDesktop ? 'w-8 h-8' : 'w-6 h-6'} text-gray-400`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <h3 className={`${isDesktop ? 'text-lg' : 'text-base'} font-medium text-gray-900 mb-2`}>
            More Content Coming Soon
          </h3>
          <p className={`${isDesktop ? 'text-sm' : 'text-xs'} text-gray-500`}>
            This space will be used for additional features like app comparisons, user reviews, and personalized recommendations.
          </p>
        </div>
      </div>
    </div>
  );
}
