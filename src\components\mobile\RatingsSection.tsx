"use client";

import { Icon } from "@iconify/react";

export default function RatingsSection() {
  // Sample data for rating distribution
  const ratingData = [
    { stars: 5, count: 12500, percentage: 83 },
    { stars: 4, count: 1800, percentage: 12 },
    { stars: 3, count: 300, percentage: 2 },
    { stars: 2, count: 0, percentage: 0 },
    { stars: 1, count: 489, percentage: 3 },
  ];

  const totalReviews = 15089;
  const averageRating = 4.9;

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Icon
          key={i}
          icon="material-symbols:star"
          className={`h-4 w-4 ${
            i <= Math.floor(rating)
              ? "text-[#1A73E8]"
              : i === Math.ceil(rating) && rating % 1 !== 0
              ? "text-[#1A73E8]"
              : "text-gray-300"
          }`}
        />
      );
    }
    return stars;
  };

  return (
    <div className="mobile-padding-sm border-t border-gray-100">
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <h2 className="mobile-heading-sm text-gray-900">Ratings and reviews</h2>
        <div className="p-1 bg-[#E7ECEF] rounded-full">
          <img
            src="/tabler_arrow-up.svg"
            alt="Arrow"
            className="mobile-icon-sm"
          />
        </div>
      </div>

      {/* Subtitle */}
      <p className="text-[#5F6368] mobile-text-sm mb-4 xs:mb-6">
        Ratings and reviews are verified
      </p>

      {/* Rating Overview - Responsive Layout */}
      <div className="flex flex-col xs:flex-row items-start mobile-gap-md xs:mobile-gap-lg">
        {/* Left side - Overall rating */}
        <div className="flex flex-col items-center w-full xs:w-auto">
          <div className="text-4xl xs:text-5xl sm:text-6xl font-medium text-gray-900 mb-2">
            {averageRating}
          </div>
          <div className="flex items-center mb-2">
            {renderStars(averageRating)}
          </div>
          <p className="text-gray-600 mobile-text-sm">
            {totalReviews.toLocaleString()}
          </p>
        </div>

        {/* Right side - Rating distribution */}
        <div className="flex-1 w-full space-y-2 xs:space-y-2">
          {ratingData.map((item) => (
            <div key={item.stars} className="flex items-center mobile-gap-sm">
              <span className="mobile-text-sm text-gray-700 w-3">
                {item.stars}
              </span>
              <div className="flex-1 h-2 xs:h-3 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className={`h-full rounded-full transition-all duration-300 ${
                    item.percentage > 0 ? "bg-[#1A73E8]" : "bg-gray-200"
                  }`}
                  style={{ width: `${item.percentage}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
