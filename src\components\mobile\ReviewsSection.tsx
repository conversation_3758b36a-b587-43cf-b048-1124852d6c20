"use client";

import { useState, useRef } from "react";
import ReviewItem from "./ReviewItem";

export default function ReviewsSection() {
  const [showAllReviews, setShowAllReviews] = useState(false);
  const reviewsSectionRef = useRef<HTMLDivElement>(null);
  const allReviews = [
    {
      id: 1,
      userAvatar: "/placeholder.svg?height=40&width=40&text=User",
      userName: "Rohan_M89",
      rating: 4,
      date: "19/07/2025",
      reviewText:
        "Super fun app! Feels like a real casino but totally risk-free. Great way to relax after work.",
      helpfulCount: 518,
    },
    {
      id: 2,
      userAvatar: "/placeholder.svg?height=40&width=40&text=User2",
      userName: "Sarah_K23",
      rating: 5,
      date: "15/07/2025",
      reviewText:
        "Amazing app! Love the variety of games and the interface is so smooth. Highly recommend!",
      helpfulCount: 342,
    },
    {
      id: 3,
      userAvatar: "/placeholder.svg?height=40&width=40&text=User3",
      userName: "Mike_Gaming",
      rating: 5,
      date: "12/07/2025",
      reviewText:
        "Best sports prediction app I've used. Great graphics and really engaging gameplay.",
      helpfulCount: 289,
    },
    {
      id: 4,
      userAvatar: "/placeholder.svg?height=40&width=40&text=User4",
      userName: "Alex_Player",
      rating: 5,
      date: "10/07/2025",
      reviewText:
        "Fantastic experience! The app runs smoothly and the games are really addictive. Perfect for entertainment.",
      helpfulCount: 156,
    },
    {
      id: 5,
      userAvatar: "/placeholder.svg?height=40&width=40&text=User5",
      userName: "Jessica_Win",
      rating: 4,
      date: "08/07/2025",
      reviewText:
        "Great app with lots of variety. Sometimes takes a while to load but overall very enjoyable.",
      helpfulCount: 203,
    },
    {
      id: 6,
      userAvatar: "/placeholder.svg?height=40&width=40&text=User6",
      userName: "David_Pro",
      rating: 5,
      date: "05/07/2025",
      reviewText:
        "Excellent graphics and smooth gameplay. The daily bonuses are a nice touch!",
      helpfulCount: 178,
    },
    {
      id: 7,
      userAvatar: "/placeholder.svg?height=40&width=40&text=User7",
      userName: "Emma_Lucky",
      rating: 4,
      date: "03/07/2025",
      reviewText:
        "Fun and engaging app. Love the different game modes available. Keeps me entertained for hours.",
      helpfulCount: 145,
    },
    {
      id: 8,
      userAvatar: "/placeholder.svg?height=40&width=40&text=User8",
      userName: "Chris_Gamer",
      rating: 5,
      date: "01/07/2025",
      reviewText:
        "Outstanding app! The user interface is intuitive and the games are really well designed.",
      helpfulCount: 267,
    },
  ];

  const reviews = showAllReviews ? allReviews : allReviews.slice(0, 4);

  const handleHelpfulClick = (reviewId: number, helpful: boolean) => {
    console.log(
      `Review ${reviewId} marked as ${helpful ? "helpful" : "not helpful"}`
    );
  };

  const handleShowLess = () => {
    // First collapse the reviews with animation
    setShowAllReviews(false);

    // Then scroll to the top of the reviews section with smooth animation
    setTimeout(() => {
      if (reviewsSectionRef.current) {
        reviewsSectionRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, 100);
  };

  return (
    <div ref={reviewsSectionRef} className="border-t border-gray-100">
      <div className="transition-all duration-300 ease-in-out">
        {reviews.map((review, index) => (
          <div
            key={review.id}
            className={`transition-all duration-300 ease-in-out transform ${
              showAllReviews && index >= 4
                ? "animate-slideInUp opacity-100"
                : index >= 4
                ? "opacity-0 -translate-y-4 scale-95"
                : "opacity-100 translate-y-0 scale-100"
            }`}
            style={{
              animationDelay:
                showAllReviews && index >= 4 ? `${(index - 4) * 80}ms` : "0ms",
            }}
          >
            <ReviewItem
              userName={review.userName}
              rating={review.rating}
              date={review.date}
              reviewText={review.reviewText}
              helpfulCount={review.helpfulCount}
              onHelpfulClick={(helpful) =>
                handleHelpfulClick(review.id, helpful)
              }
            />
          </div>
        ))}
      </div>

      {/* More reviews / Show less button - Responsive */}
      <div className="mobile-padding-sm border-t border-gray-100">
        {!showAllReviews ? (
          <button
            className="text-blue-600 font-medium mobile-text-base hover:text-blue-700 transition-colors touch-manipulation"
            onClick={() => setShowAllReviews(true)}
          >
            More reviews
          </button>
        ) : (
          <button
            className="text-blue-600 font-medium mobile-text-base hover:text-blue-700 transition-colors touch-manipulation"
            onClick={handleShowLess}
          >
            Show less
          </button>
        )}
      </div>
    </div>
  );
}
