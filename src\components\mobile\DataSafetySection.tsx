export const DataSafetySection = () => {
  return (
    <div className="mobile-padding-sm border-t border-gray-100">
      <div className="flex items-center justify-between mb-3">
        <h2 className="mobile-heading-sm text-gray-900">Data Safety</h2>
        <div className="p-1 bg-[#E7ECEF] rounded-full">
          <img
            src="/tabler_arrow-up.svg"
            alt="Arrow"
            className="mobile-icon-sm"
          />
        </div>
      </div>
      <p className="text-gray-700 text-justify mobile-text-sm leading-relaxed mb-4">
        Safety starts with understanding how developers collect and share your
        data. Data privacy and security practices may vary based on your use,
        region, and age. The developer provided this information and may update
        it over time.
      </p>
    </div>
  );
};
