"use client";

import { Icon } from "@iconify/react";
import { useState } from "react";

interface NavItem {
  id: string;
  label: string;
  icon: string;
}

const navItems: NavItem[] = [
  {
    id: "games",
    label: "Games",
    icon: "mdi:gamepad-variant",
  },
  {
    id: "apps",
    label: "Apps",
    icon: "mdi:view-grid",
  },
  {
    id: "movies",
    label: "Movies",
    icon: "mdi:movie-open",
  },
  {
    id: "books",
    label: "Books",
    icon: "mdi:book-open-page-variant",
  },
  {
    id: "kids",
    label: "Kids",
    icon: "simple-icons:youtubekids",
  },
];

export default function NavigationBar() {
  const [activeItem, setActiveItem] = useState("apps");

  return (
    <div className="bg-[#EEF9FF] border-t border-gray-200 shadow-lg mobile-safe-bottom">
      <nav className="flex items-center justify-around mobile-padding-xs">
        {navItems.map((item) => (
          <button
            key={item.id}
            onClick={() => setActiveItem(item.id)}
            className={`flex flex-col items-center mobile-gap-xs px-2 xs:px-3 py-2 rounded-lg transition-colors touch-manipulation ${
              activeItem === item.id
                ? "text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            <Icon icon={item.icon} className="mobile-icon-sm" />
            <span className="mobile-text-xs font-medium">{item.label}</span>
          </button>
        ))}
      </nav>
    </div>
  );
}
